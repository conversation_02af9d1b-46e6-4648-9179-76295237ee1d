/**
 * Unit Tests for SessionTimerBar Component
 * 
 * Tests the session timer bar component including timer controls,
 * session management, and timer instance operations.
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { SessionTimerBar } from '../SessionTimerBar';
import { TaskSession, TimerInstance } from '../../../types/timer';
import { Task } from '../../../types/task';

// Mock dependencies
const mockShowError = vi.fn();
const mockShowSuccess = vi.fn();
vi.mock('../../../contexts/NotificationContext', () => ({
  useNotification: () => ({
    showError: mockShowError,
    showSuccess: mockShowSuccess,
  }),
}));

describe('SessionTimerBar', () => {
  const mockTasks: Task[] = [
    {
      id: 'task-1',
      name: 'Development Work',
      description: 'Software development tasks',
      createdAt: '2024-01-15T09:00:00Z',
      updatedAt: '2024-01-15T09:00:00Z',
    },
    {
      id: 'task-2',
      name: 'Code Review',
      description: 'Review pull requests',
      createdAt: '2024-01-15T10:00:00Z',
      updatedAt: '2024-01-15T10:00:00Z',
    },
  ];

  const mockTimerInstance: TimerInstance = {
    id: 'instance-1',
    sessionId: 'session-1',
    startTime: new Date('2024-01-15T10:00:00Z'),
    endTime: undefined,
    duration: 0,
    isRunning: true,
    isPaused: false,
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z',
  };

  const mockActiveSession: TaskSession = {
    id: 'session-1',
    taskId: 'task-1',
    taskName: 'Development Work',
    timerInstances: [mockTimerInstance],
    totalDuration: 3600000, // 1 hour
    isActive: true,
    date: '2024-01-15',
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z',
  };

  const mockProps = {
    activeSession: null as TaskSession | null,
    predefinedTasks: mockTasks,
    onStartSession: vi.fn(),
    onStopSession: vi.fn(),
    onCreateTimerInstance: vi.fn(),
    onStartTimer: vi.fn(),
    onStopTimer: vi.fn(),
    onPauseTimer: vi.fn(),
    onResumeTimer: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('No Active Session State', () => {
    it('should render start session controls when no active session', () => {
      render(<SessionTimerBar {...mockProps} />);
      
      expect(screen.getByText('Start New Session')).toBeInTheDocument();
      expect(screen.getByPlaceholderText('Select or enter task name...')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /start new session/i })).toBeInTheDocument();
    });

    it('should disable start button when no task is selected', () => {
      render(<SessionTimerBar {...mockProps} />);
      
      const startButton = screen.getByRole('button', { name: /start new session/i });
      expect(startButton).toBeDisabled();
    });

    it('should enable start button when task is selected', () => {
      render(<SessionTimerBar {...mockProps} />);
      
      const taskInput = screen.getByPlaceholderText('Select or enter task name...');
      fireEvent.change(taskInput, { target: { value: 'Development Work' } });
      
      const startButton = screen.getByRole('button', { name: /start new session/i });
      expect(startButton).not.toBeDisabled();
    });

    it('should call onStartSession when start button is clicked with selected task', () => {
      render(<SessionTimerBar {...mockProps} />);
      
      const taskInput = screen.getByPlaceholderText('Select or enter task name...');
      fireEvent.change(taskInput, { target: { value: 'Development Work' } });
      
      const startButton = screen.getByRole('button', { name: /start new session/i });
      fireEvent.click(startButton);
      
      expect(mockProps.onStartSession).toHaveBeenCalledWith('task-1', 'Development Work');
    });

    it('should handle custom task name input', () => {
      render(<SessionTimerBar {...mockProps} />);
      
      const taskInput = screen.getByPlaceholderText('Select or enter task name...');
      fireEvent.change(taskInput, { target: { value: 'Custom Task Name' } });
      
      const startButton = screen.getByRole('button', { name: /start new session/i });
      fireEvent.click(startButton);
      
      expect(mockProps.onStartSession).toHaveBeenCalledWith(null, 'Custom Task Name');
    });
  });

  describe('Active Session State', () => {
    const propsWithActiveSession = {
      ...mockProps,
      activeSession: mockActiveSession,
    };

    it('should render active session information', () => {
      render(<SessionTimerBar {...propsWithActiveSession} />);
      
      expect(screen.getByText('Development Work')).toBeInTheDocument();
      expect(screen.getByText('1 timer')).toBeInTheDocument();
      expect(screen.getByText('1:00:00')).toBeInTheDocument(); // Total duration
    });

    it('should show End Session button when session is active', () => {
      render(<SessionTimerBar {...propsWithActiveSession} />);
      
      expect(screen.getByRole('button', { name: /end session/i })).toBeInTheDocument();
    });

    it('should call onStopSession when End Session button is clicked', () => {
      render(<SessionTimerBar {...propsWithActiveSession} />);
      
      const endButton = screen.getByRole('button', { name: /end session/i });
      fireEvent.click(endButton);
      
      expect(mockProps.onStopSession).toHaveBeenCalledWith('session-1');
    });

    it('should show Add Timer button when session is active', () => {
      render(<SessionTimerBar {...propsWithActiveSession} />);
      
      expect(screen.getByRole('button', { name: /add timer/i })).toBeInTheDocument();
    });

    it('should call onCreateTimerInstance when Add Timer button is clicked', () => {
      render(<SessionTimerBar {...propsWithActiveSession} />);
      
      const addTimerButton = screen.getByRole('button', { name: /add timer/i });
      fireEvent.click(addTimerButton);
      
      expect(mockProps.onCreateTimerInstance).toHaveBeenCalledWith('session-1');
    });
  });

  describe('Timer Instance Controls', () => {
    const runningSession = {
      ...mockActiveSession,
      timerInstances: [
        {
          ...mockTimerInstance,
          isRunning: true,
          isPaused: false,
        },
      ],
    };

    const pausedSession = {
      ...mockActiveSession,
      timerInstances: [
        {
          ...mockTimerInstance,
          isRunning: false,
          isPaused: true,
        },
      ],
    };

    const stoppedSession = {
      ...mockActiveSession,
      timerInstances: [
        {
          ...mockTimerInstance,
          isRunning: false,
          isPaused: false,
          endTime: new Date('2024-01-15T11:00:00Z'),
        },
      ],
    };

    it('should show pause button for running timer', () => {
      render(<SessionTimerBar {...mockProps} activeSession={runningSession} />);
      
      expect(screen.getByRole('button', { name: /pause/i })).toBeInTheDocument();
    });

    it('should show resume button for paused timer', () => {
      render(<SessionTimerBar {...mockProps} activeSession={pausedSession} />);
      
      expect(screen.getByRole('button', { name: /resume/i })).toBeInTheDocument();
    });

    it('should show start button for stopped timer', () => {
      render(<SessionTimerBar {...mockProps} activeSession={stoppedSession} />);
      
      expect(screen.getByRole('button', { name: /start/i })).toBeInTheDocument();
    });

    it('should call onPauseTimer when pause button is clicked', () => {
      render(<SessionTimerBar {...mockProps} activeSession={runningSession} />);
      
      const pauseButton = screen.getByRole('button', { name: /pause/i });
      fireEvent.click(pauseButton);
      
      expect(mockProps.onPauseTimer).toHaveBeenCalledWith('instance-1');
    });

    it('should call onResumeTimer when resume button is clicked', () => {
      render(<SessionTimerBar {...mockProps} activeSession={pausedSession} />);
      
      const resumeButton = screen.getByRole('button', { name: /resume/i });
      fireEvent.click(resumeButton);
      
      expect(mockProps.onResumeTimer).toHaveBeenCalledWith('instance-1');
    });

    it('should call onStartTimer when start button is clicked', () => {
      render(<SessionTimerBar {...mockProps} activeSession={stoppedSession} />);
      
      const startButton = screen.getByRole('button', { name: /start/i });
      fireEvent.click(startButton);
      
      expect(mockProps.onStartTimer).toHaveBeenCalledWith('instance-1');
    });

    it('should call onStopTimer when stop button is clicked', () => {
      render(<SessionTimerBar {...mockProps} activeSession={runningSession} />);
      
      const stopButton = screen.getByRole('button', { name: /stop/i });
      fireEvent.click(stopButton);
      
      expect(mockProps.onStopTimer).toHaveBeenCalledWith('instance-1');
    });
  });

  describe('Multiple Timer Instances', () => {
    const multiTimerSession = {
      ...mockActiveSession,
      timerInstances: [
        { ...mockTimerInstance, id: 'instance-1', isRunning: true },
        { ...mockTimerInstance, id: 'instance-2', isRunning: false, isPaused: true },
        { ...mockTimerInstance, id: 'instance-3', isRunning: false, isPaused: false },
      ],
    };

    it('should display correct timer count for multiple timers', () => {
      render(<SessionTimerBar {...mockProps} activeSession={multiTimerSession} />);
      
      expect(screen.getByText('3 timers')).toBeInTheDocument();
    });

    it('should show controls for each timer instance', () => {
      render(<SessionTimerBar {...mockProps} activeSession={multiTimerSession} />);
      
      // Should have controls for each timer
      expect(screen.getAllByRole('button', { name: /pause|resume|start|stop/i })).toHaveLength(3);
    });
  });

  describe('Error Handling', () => {
    it('should handle missing task gracefully', () => {
      const sessionWithMissingTask = {
        ...mockActiveSession,
        taskName: 'Non-existent Task',
        taskId: 'non-existent',
      };

      render(<SessionTimerBar {...mockProps} activeSession={sessionWithMissingTask} />);
      
      expect(screen.getByText('Non-existent Task')).toBeInTheDocument();
    });

    it('should handle empty timer instances array', () => {
      const sessionWithNoTimers = {
        ...mockActiveSession,
        timerInstances: [],
      };

      render(<SessionTimerBar {...mockProps} activeSession={sessionWithNoTimers} />);
      
      expect(screen.getByText('0 timers')).toBeInTheDocument();
    });
  });
});

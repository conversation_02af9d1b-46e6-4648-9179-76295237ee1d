/**
 * Unit Tests for SessionList Component
 * 
 * Tests the session list component including session display,
 * selection, activation, and management operations.
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { SessionList } from '../SessionList';
import { TaskSession, TimerInstance } from '../../../../types/timer';

describe('SessionList', () => {
  const mockTimerInstance: TimerInstance = {
    id: 'instance-1',
    sessionId: 'session-1',
    startTime: new Date('2024-01-15T10:00:00Z'),
    endTime: new Date('2024-01-15T11:00:00Z'),
    duration: 3600000, // 1 hour
    isRunning: false,
    isPaused: false,
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T11:00:00Z',
  };

  const mockSessions: TaskSession[] = [
    {
      id: 'session-1',
      taskId: 'task-1',
      taskName: 'Development Work',
      timerInstances: [mockTimerInstance],
      totalDuration: 3600000, // 1 hour
      isActive: false,
      date: '2024-01-15',
      createdAt: '2024-01-15T10:00:00Z',
      updatedAt: '2024-01-15T11:00:00Z',
    },
    {
      id: 'session-2',
      taskId: 'task-2',
      taskName: 'Code Review',
      timerInstances: [
        {
          ...mockTimerInstance,
          id: 'instance-2',
          sessionId: 'session-2',
          isRunning: true,
          endTime: undefined,
        },
      ],
      totalDuration: 1800000, // 30 minutes
      isActive: true,
      date: '2024-01-15',
      createdAt: '2024-01-15T11:00:00Z',
      updatedAt: '2024-01-15T11:30:00Z',
    },
    {
      id: 'session-3',
      taskId: 'task-3',
      taskName: 'Documentation',
      timerInstances: [],
      totalDuration: 0,
      isActive: false,
      date: '2024-01-15',
      createdAt: '2024-01-15T12:00:00Z',
      updatedAt: '2024-01-15T12:00:00Z',
    },
  ];

  const mockProps = {
    sessions: mockSessions,
    activeSession: mockSessions[1],
    selectedSession: null as TaskSession | null,
    onSessionSelect: vi.fn(),
    onSessionActivate: vi.fn(),
    onSessionDeactivate: vi.fn(),
    onSessionDelete: vi.fn(),
    isLoading: false,
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Rendering', () => {
    it('should render list of sessions', () => {
      render(<SessionList {...mockProps} />);
      
      expect(screen.getByText('Development Work')).toBeInTheDocument();
      expect(screen.getByText('Code Review')).toBeInTheDocument();
      expect(screen.getByText('Documentation')).toBeInTheDocument();
    });

    it('should display session durations correctly', () => {
      render(<SessionList {...mockProps} />);
      
      expect(screen.getByText('1:00:00')).toBeInTheDocument(); // 1 hour
      expect(screen.getByText('0:30:00')).toBeInTheDocument(); // 30 minutes
      expect(screen.getByText('0:00:00')).toBeInTheDocument(); // 0 minutes
    });

    it('should display timer instance counts', () => {
      render(<SessionList {...mockProps} />);
      
      expect(screen.getByText('1 timer')).toBeInTheDocument();
      expect(screen.getByText('0 timers')).toBeInTheDocument();
    });

    it('should show loading state when isLoading is true', () => {
      render(<SessionList {...mockProps} isLoading={true} />);
      
      expect(screen.getByRole('progressbar')).toBeInTheDocument();
    });

    it('should show empty state when no sessions', () => {
      render(<SessionList {...mockProps} sessions={[]} />);
      
      expect(screen.getByText('No sessions found')).toBeInTheDocument();
      expect(screen.getByText('Start your first session to begin tracking time')).toBeInTheDocument();
    });
  });

  describe('Session States', () => {
    it('should highlight active session', () => {
      render(<SessionList {...mockProps} />);
      
      const activeSessionCard = screen.getByText('Code Review').closest('[data-testid="session-card"]');
      expect(activeSessionCard).toHaveClass('active');
    });

    it('should highlight selected session', () => {
      const propsWithSelection = {
        ...mockProps,
        selectedSession: mockSessions[0],
      };
      
      render(<SessionList {...propsWithSelection} />);
      
      const selectedSessionCard = screen.getByText('Development Work').closest('[data-testid="session-card"]');
      expect(selectedSessionCard).toHaveClass('selected');
    });

    it('should show running indicator for sessions with running timers', () => {
      render(<SessionList {...mockProps} />);
      
      const runningSession = screen.getByText('Code Review').closest('[data-testid="session-card"]');
      expect(runningSession).toContainElement(screen.getByTestId('running-indicator'));
    });

    it('should show paused indicator for sessions with paused timers', () => {
      const sessionsWithPaused = [
        {
          ...mockSessions[0],
          timerInstances: [
            {
              ...mockTimerInstance,
              isRunning: false,
              isPaused: true,
            },
          ],
        },
      ];
      
      render(<SessionList {...mockProps} sessions={sessionsWithPaused} />);
      
      const pausedSession = screen.getByText('Development Work').closest('[data-testid="session-card"]');
      expect(pausedSession).toContainElement(screen.getByTestId('paused-indicator'));
    });
  });

  describe('User Interactions', () => {
    it('should call onSessionSelect when session is clicked', () => {
      render(<SessionList {...mockProps} />);
      
      const sessionCard = screen.getByText('Development Work').closest('[data-testid="session-card"]');
      fireEvent.click(sessionCard!);
      
      expect(mockProps.onSessionSelect).toHaveBeenCalledWith(mockSessions[0]);
    });

    it('should call onSessionActivate when activate button is clicked', () => {
      render(<SessionList {...mockProps} />);
      
      const activateButton = screen.getAllByRole('button', { name: /activate/i })[0];
      fireEvent.click(activateButton);
      
      expect(mockProps.onSessionActivate).toHaveBeenCalledWith('session-1');
    });

    it('should call onSessionDeactivate when deactivate button is clicked', () => {
      render(<SessionList {...mockProps} />);
      
      const deactivateButton = screen.getByRole('button', { name: /deactivate/i });
      fireEvent.click(deactivateButton);
      
      expect(mockProps.onSessionDeactivate).toHaveBeenCalledWith('session-2');
    });

    it('should call onSessionDelete when delete button is clicked', () => {
      render(<SessionList {...mockProps} />);
      
      const deleteButton = screen.getAllByRole('button', { name: /delete/i })[0];
      fireEvent.click(deleteButton);
      
      expect(mockProps.onSessionDelete).toHaveBeenCalledWith('session-1');
    });

    it('should show confirmation dialog before deleting session', async () => {
      render(<SessionList {...mockProps} />);
      
      const deleteButton = screen.getAllByRole('button', { name: /delete/i })[0];
      fireEvent.click(deleteButton);
      
      expect(screen.getByText('Delete Session')).toBeInTheDocument();
      expect(screen.getByText('Are you sure you want to delete this session?')).toBeInTheDocument();
      
      const confirmButton = screen.getByRole('button', { name: /confirm/i });
      fireEvent.click(confirmButton);
      
      await waitFor(() => {
        expect(mockProps.onSessionDelete).toHaveBeenCalledWith('session-1');
      });
    });
  });

  describe('Session Details', () => {
    it('should display session creation date', () => {
      render(<SessionList {...mockProps} />);
      
      expect(screen.getByText('Jan 15, 2024')).toBeInTheDocument();
    });

    it('should display session task information', () => {
      render(<SessionList {...mockProps} />);
      
      expect(screen.getByText('Development Work')).toBeInTheDocument();
      expect(screen.getByText('Code Review')).toBeInTheDocument();
      expect(screen.getByText('Documentation')).toBeInTheDocument();
    });

    it('should show session notes when available', () => {
      const sessionsWithNotes = [
        {
          ...mockSessions[0],
          notes: 'Working on user authentication feature',
        },
      ];
      
      render(<SessionList {...mockProps} sessions={sessionsWithNotes} />);
      
      expect(screen.getByText('Working on user authentication feature')).toBeInTheDocument();
    });

    it('should truncate long session notes', () => {
      const longNote = 'This is a very long note that should be truncated when displayed in the session list to prevent the UI from becoming cluttered and unreadable';
      const sessionsWithLongNotes = [
        {
          ...mockSessions[0],
          notes: longNote,
        },
      ];
      
      render(<SessionList {...mockProps} sessions={sessionsWithLongNotes} />);
      
      const noteElement = screen.getByText(/This is a very long note/);
      expect(noteElement.textContent).toContain('...');
      expect(noteElement.textContent!.length).toBeLessThan(longNote.length);
    });
  });

  describe('Keyboard Navigation', () => {
    it('should support keyboard navigation between sessions', () => {
      render(<SessionList {...mockProps} />);
      
      const firstSession = screen.getByText('Development Work').closest('[data-testid="session-card"]');
      firstSession!.focus();
      
      fireEvent.keyDown(firstSession!, { key: 'ArrowDown' });
      
      const secondSession = screen.getByText('Code Review').closest('[data-testid="session-card"]');
      expect(document.activeElement).toBe(secondSession);
    });

    it('should activate session on Enter key', () => {
      render(<SessionList {...mockProps} />);
      
      const sessionCard = screen.getByText('Development Work').closest('[data-testid="session-card"]');
      fireEvent.keyDown(sessionCard!, { key: 'Enter' });
      
      expect(mockProps.onSessionSelect).toHaveBeenCalledWith(mockSessions[0]);
    });

    it('should delete session on Delete key', () => {
      render(<SessionList {...mockProps} />);
      
      const sessionCard = screen.getByText('Development Work').closest('[data-testid="session-card"]');
      fireEvent.keyDown(sessionCard!, { key: 'Delete' });
      
      expect(screen.getByText('Delete Session')).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('should have proper ARIA labels', () => {
      render(<SessionList {...mockProps} />);
      
      expect(screen.getByRole('list')).toHaveAttribute('aria-label', 'Session list');
      
      const sessionCards = screen.getAllByRole('listitem');
      sessionCards.forEach((card, index) => {
        expect(card).toHaveAttribute('aria-label', expect.stringContaining(mockSessions[index].taskName));
      });
    });

    it('should announce session state changes', () => {
      const { rerender } = render(<SessionList {...mockProps} />);
      
      const updatedProps = {
        ...mockProps,
        activeSession: mockSessions[0], // Change active session
      };
      
      rerender(<SessionList {...updatedProps} />);
      
      const newActiveSession = screen.getByText('Development Work').closest('[data-testid="session-card"]');
      expect(newActiveSession).toHaveAttribute('aria-live', 'polite');
    });
  });
});

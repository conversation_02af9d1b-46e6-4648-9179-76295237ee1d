/**
 * Unit Tests for InactivityWarningDialog Component
 * 
 * Tests the inactivity warning dialog including countdown display,
 * user actions, and timer integration.
 */

import React from 'react';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import { InactivityWarningDialog } from '../InactivityWarningDialog';

// Mock timer for countdown testing
vi.useFakeTimers();

describe('InactivityWarningDialog', () => {
  const mockProps = {
    remainingSeconds: 30,
    onContinue: vi.fn(),
    onPause: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
    vi.clearAllTimers();
  });

  afterEach(() => {
    vi.runOnlyPendingTimers();
    vi.useRealTimers();
    vi.useFakeTimers();
  });

  describe('Rendering', () => {
    it('should render dialog when remainingSeconds > 0', () => {
      render(<InactivityWarningDialog {...mockProps} />);
      
      expect(screen.getByText('Inactivity Detected')).toBeInTheDocument();
      expect(screen.getByText(/No activity detected for the configured time period/)).toBeInTheDocument();
    });

    it('should not render dialog when remainingSeconds is 0', () => {
      render(<InactivityWarningDialog {...mockProps} remainingSeconds={0} />);
      
      expect(screen.queryByText('Inactivity Detected')).not.toBeInTheDocument();
    });

    it('should display remaining time correctly', () => {
      render(<InactivityWarningDialog {...mockProps} remainingSeconds={15} />);
      
      expect(screen.getByText(/15s/)).toBeInTheDocument();
    });

    it('should show warning icon', () => {
      render(<InactivityWarningDialog {...mockProps} />);
      
      const warningIcon = screen.getByTestId('WarningIcon');
      expect(warningIcon).toBeInTheDocument();
    });

    it('should display progress bar', () => {
      render(<InactivityWarningDialog {...mockProps} />);
      
      const progressBar = screen.getByRole('progressbar');
      expect(progressBar).toBeInTheDocument();
    });
  });

  describe('Countdown Display', () => {
    it('should display countdown in seconds format', () => {
      const { rerender } = render(<InactivityWarningDialog {...mockProps} remainingSeconds={45} />);
      
      expect(screen.getByText(/45s/)).toBeInTheDocument();
      
      rerender(<InactivityWarningDialog {...mockProps} remainingSeconds={1} />);
      expect(screen.getByText(/1s/)).toBeInTheDocument();
    });

    it('should update progress bar based on remaining time', () => {
      const { rerender } = render(<InactivityWarningDialog {...mockProps} remainingSeconds={30} />);
      
      const progressBar = screen.getByRole('progressbar');
      expect(progressBar).toHaveAttribute('aria-valuenow', '100'); // 30/30 = 100%
      
      rerender(<InactivityWarningDialog {...mockProps} remainingSeconds={15} />);
      expect(progressBar).toHaveAttribute('aria-valuenow', '50'); // 15/30 = 50%
      
      rerender(<InactivityWarningDialog {...mockProps} remainingSeconds={0} />);
      expect(screen.queryByRole('progressbar')).not.toBeInTheDocument(); // Dialog should be closed
    });

    it('should handle edge case of very low remaining time', () => {
      render(<InactivityWarningDialog {...mockProps} remainingSeconds={1} />);
      
      expect(screen.getByText(/1s/)).toBeInTheDocument();
      
      const progressBar = screen.getByRole('progressbar');
      expect(progressBar).toHaveAttribute('aria-valuenow', '3.33'); // 1/30 ≈ 3.33%
    });
  });

  describe('User Actions', () => {
    it('should call onContinue when Continue Working button is clicked', () => {
      render(<InactivityWarningDialog {...mockProps} />);
      
      const continueButton = screen.getByRole('button', { name: /continue working/i });
      fireEvent.click(continueButton);
      
      expect(mockProps.onContinue).toHaveBeenCalledTimes(1);
    });

    it('should call onPause when Pause Timer button is clicked', () => {
      render(<InactivityWarningDialog {...mockProps} />);
      
      const pauseButton = screen.getByRole('button', { name: /pause timer/i });
      fireEvent.click(pauseButton);
      
      expect(mockProps.onPause).toHaveBeenCalledTimes(1);
    });

    it('should have proper button icons', () => {
      render(<InactivityWarningDialog {...mockProps} />);
      
      expect(screen.getByTestId('PlayArrowIcon')).toBeInTheDocument(); // Continue icon
      expect(screen.getByTestId('PauseIcon')).toBeInTheDocument(); // Pause icon
    });

    it('should prevent dialog from closing with escape key', () => {
      render(<InactivityWarningDialog {...mockProps} />);
      
      const dialog = screen.getByRole('dialog');
      fireEvent.keyDown(dialog, { key: 'Escape' });
      
      // Dialog should still be open
      expect(screen.getByText('Inactivity Detected')).toBeInTheDocument();
    });
  });

  describe('Dialog Behavior', () => {
    it('should be modal and block interaction with background', () => {
      render(<InactivityWarningDialog {...mockProps} />);
      
      const dialog = screen.getByRole('dialog');
      expect(dialog).toHaveAttribute('aria-modal', 'true');
    });

    it('should have proper accessibility attributes', () => {
      render(<InactivityWarningDialog {...mockProps} />);
      
      const dialog = screen.getByRole('dialog');
      expect(dialog).toHaveAttribute('aria-labelledby');
      expect(dialog).toHaveAttribute('aria-describedby');
    });

    it('should focus on dialog when opened', () => {
      render(<InactivityWarningDialog {...mockProps} />);
      
      const dialog = screen.getByRole('dialog');
      expect(document.activeElement).toBe(dialog);
    });

    it('should have proper dialog structure', () => {
      render(<InactivityWarningDialog {...mockProps} />);
      
      expect(screen.getByRole('dialog')).toBeInTheDocument();
      expect(screen.getByText('Inactivity Detected')).toBeInTheDocument();
      expect(screen.getByText(/No activity detected/)).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /continue working/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /pause timer/i })).toBeInTheDocument();
    });
  });

  describe('Warning Messages', () => {
    it('should display appropriate warning message', () => {
      render(<InactivityWarningDialog {...mockProps} />);
      
      expect(screen.getByText('No activity detected for the configured time period.')).toBeInTheDocument();
    });

    it('should display countdown message with remaining time', () => {
      render(<InactivityWarningDialog {...mockProps} remainingSeconds={25} />);
      
      expect(screen.getByText(/Your timer will be automatically paused in.*25s.*unless you continue working/)).toBeInTheDocument();
    });

    it('should update countdown message when time changes', () => {
      const { rerender } = render(<InactivityWarningDialog {...mockProps} remainingSeconds={10} />);
      
      expect(screen.getByText(/10s/)).toBeInTheDocument();
      
      rerender(<InactivityWarningDialog {...mockProps} remainingSeconds={5} />);
      expect(screen.getByText(/5s/)).toBeInTheDocument();
    });
  });

  describe('Edge Cases', () => {
    it('should handle zero remaining seconds', () => {
      render(<InactivityWarningDialog {...mockProps} remainingSeconds={0} />);
      
      expect(screen.queryByRole('dialog')).not.toBeInTheDocument();
    });

    it('should handle negative remaining seconds', () => {
      render(<InactivityWarningDialog {...mockProps} remainingSeconds={-5} />);
      
      expect(screen.queryByRole('dialog')).not.toBeInTheDocument();
    });

    it('should handle very large remaining seconds', () => {
      render(<InactivityWarningDialog {...mockProps} remainingSeconds={999} />);
      
      expect(screen.getByText(/999s/)).toBeInTheDocument();
      
      const progressBar = screen.getByRole('progressbar');
      // Progress should be capped at 100%
      const progressValue = parseFloat(progressBar.getAttribute('aria-valuenow') || '0');
      expect(progressValue).toBeGreaterThanOrEqual(0);
      expect(progressValue).toBeLessThanOrEqual(100);
    });

    it('should handle missing callback functions gracefully', () => {
      const propsWithoutCallbacks = {
        remainingSeconds: 30,
        onContinue: undefined as any,
        onPause: undefined as any,
      };
      
      expect(() => {
        render(<InactivityWarningDialog {...propsWithoutCallbacks} />);
      }).not.toThrow();
    });
  });

  describe('Styling and Layout', () => {
    it('should have proper dialog styling', () => {
      render(<InactivityWarningDialog {...mockProps} />);
      
      const dialog = screen.getByRole('dialog');
      expect(dialog).toHaveClass('MuiDialog-root');
    });

    it('should have warning color scheme', () => {
      render(<InactivityWarningDialog {...mockProps} />);
      
      const warningAlert = screen.getByRole('alert');
      expect(warningAlert).toHaveClass('MuiAlert-standardWarning');
    });

    it('should have proper button styling', () => {
      render(<InactivityWarningDialog {...mockProps} />);
      
      const continueButton = screen.getByRole('button', { name: /continue working/i });
      const pauseButton = screen.getByRole('button', { name: /pause timer/i });
      
      expect(continueButton).toHaveClass('MuiButton-containedPrimary');
      expect(pauseButton).toHaveClass('MuiButton-outlined');
    });
  });

  describe('Integration with Timer Events', () => {
    it('should handle rapid state changes', () => {
      const { rerender } = render(<InactivityWarningDialog {...mockProps} remainingSeconds={5} />);
      
      // Rapidly change remaining seconds
      rerender(<InactivityWarningDialog {...mockProps} remainingSeconds={4} />);
      rerender(<InactivityWarningDialog {...mockProps} remainingSeconds={3} />);
      rerender(<InactivityWarningDialog {...mockProps} remainingSeconds={2} />);
      rerender(<InactivityWarningDialog {...mockProps} remainingSeconds={1} />);
      rerender(<InactivityWarningDialog {...mockProps} remainingSeconds={0} />);
      
      // Dialog should be closed at the end
      expect(screen.queryByRole('dialog')).not.toBeInTheDocument();
    });

    it('should handle reopening after closing', () => {
      const { rerender } = render(<InactivityWarningDialog {...mockProps} remainingSeconds={0} />);
      
      expect(screen.queryByRole('dialog')).not.toBeInTheDocument();
      
      rerender(<InactivityWarningDialog {...mockProps} remainingSeconds={30} />);
      expect(screen.getByRole('dialog')).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('should announce dialog opening to screen readers', () => {
      render(<InactivityWarningDialog {...mockProps} />);
      
      const dialog = screen.getByRole('dialog');
      expect(dialog).toHaveAttribute('aria-live', 'assertive');
    });

    it('should have proper heading hierarchy', () => {
      render(<InactivityWarningDialog {...mockProps} />);
      
      const heading = screen.getByRole('heading', { name: /inactivity detected/i });
      expect(heading).toBeInTheDocument();
    });

    it('should have keyboard navigation support', () => {
      render(<InactivityWarningDialog {...mockProps} />);
      
      const continueButton = screen.getByRole('button', { name: /continue working/i });
      const pauseButton = screen.getByRole('button', { name: /pause timer/i });
      
      // Tab navigation should work
      continueButton.focus();
      expect(document.activeElement).toBe(continueButton);
      
      fireEvent.keyDown(continueButton, { key: 'Tab' });
      expect(document.activeElement).toBe(pauseButton);
    });
  });
});

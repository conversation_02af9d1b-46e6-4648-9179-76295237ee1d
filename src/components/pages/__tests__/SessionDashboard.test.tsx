/**
 * Unit Tests for SessionDashboard Component
 * 
 * Tests the main session management dashboard including session creation,
 * display, activation, and timer management functionality.
 */

import React from 'react';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import { SessionDashboard } from '../SessionDashboard';
import { TaskSession, TimerInstance } from '../../../types/timer';
import { Task } from '../../../types/task';

// Mock dependencies
const mockInvoke = vi.fn();
vi.mock('@tauri-apps/api/tauri', () => ({
  invoke: mockInvoke,
}));

const mockShowError = vi.fn();
const mockShowSuccess = vi.fn();
const mockShowWarning = vi.fn();
vi.mock('../../../contexts/NotificationContext', () => ({
  useNotification: () => ({
    showError: mockShowError,
    showSuccess: mockShowSuccess,
    showWarning: mockShowWarning,
  }),
}));

// Mock session management hook
const mockCreateSession = vi.fn();
const mockUpdateSession = vi.fn();
const mockDeleteSession = vi.fn();
const mockCreateTimerInstance = vi.fn();
const mockStartTimer = vi.fn();
const mockStopTimer = vi.fn();
const mockPauseTimer = vi.fn();
const mockResumeTimer = vi.fn();

vi.mock('../../../hooks/useSessionManagement', () => ({
  useSessionManagement: () => ({
    sessions: [],
    activeSession: null,
    isLoading: false,
    error: null,
    createSession: mockCreateSession,
    updateSession: mockUpdateSession,
    deleteSession: mockDeleteSession,
    createTimerInstance: mockCreateTimerInstance,
    startTimer: mockStartTimer,
    stopTimer: mockStopTimer,
    pauseTimer: mockPauseTimer,
    resumeTimer: mockResumeTimer,
    refreshSessions: vi.fn(),
  }),
}));

// Mock task management
const mockTasks: Task[] = [
  {
    id: 'task-1',
    name: 'Development Work',
    description: 'Software development tasks',
    createdAt: '2024-01-15T09:00:00Z',
    updatedAt: '2024-01-15T09:00:00Z',
  },
  {
    id: 'task-2',
    name: 'Code Review',
    description: 'Review pull requests',
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z',
  },
];

vi.mock('../../../hooks/useTaskManagement', () => ({
  useTaskManagement: () => ({
    tasks: mockTasks,
    isLoading: false,
    error: null,
  }),
}));

describe('SessionDashboard', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.clearAllTimers();
  });

  describe('Initial Rendering', () => {
    it('should render the dashboard with no sessions', () => {
      render(<SessionDashboard />);
      
      expect(screen.getByText('Sessions')).toBeInTheDocument();
      expect(screen.getByText('Start New Session')).toBeInTheDocument();
      expect(screen.getByPlaceholderText('Select or enter task name...')).toBeInTheDocument();
    });

    it('should show loading state when sessions are loading', () => {
      vi.mocked(require('../../../hooks/useSessionManagement').useSessionManagement).mockReturnValue({
        sessions: [],
        activeSession: null,
        isLoading: true,
        error: null,
        createSession: mockCreateSession,
        updateSession: mockUpdateSession,
        deleteSession: mockDeleteSession,
        createTimerInstance: mockCreateTimerInstance,
        startTimer: mockStartTimer,
        stopTimer: mockStopTimer,
        pauseTimer: mockPauseTimer,
        resumeTimer: mockResumeTimer,
        refreshSessions: vi.fn(),
      });

      render(<SessionDashboard />);
      
      expect(screen.getByRole('progressbar')).toBeInTheDocument();
    });

    it('should show error state when there is an error', () => {
      const errorMessage = 'Failed to load sessions';
      vi.mocked(require('../../../hooks/useSessionManagement').useSessionManagement).mockReturnValue({
        sessions: [],
        activeSession: null,
        isLoading: false,
        error: new Error(errorMessage),
        createSession: mockCreateSession,
        updateSession: mockUpdateSession,
        deleteSession: mockDeleteSession,
        createTimerInstance: mockCreateTimerInstance,
        startTimer: mockStartTimer,
        stopTimer: mockStopTimer,
        pauseTimer: mockPauseTimer,
        resumeTimer: mockResumeTimer,
        refreshSessions: vi.fn(),
      });

      render(<SessionDashboard />);
      
      expect(screen.getByText(errorMessage)).toBeInTheDocument();
    });
  });

  describe('Session Creation', () => {
    it('should create a new session when task is selected and start button clicked', async () => {
      const newSession: TaskSession = {
        id: 'session-1',
        taskId: 'task-1',
        taskName: 'Development Work',
        timerInstances: [],
        totalDuration: 0,
        isActive: true,
        date: '2024-01-15',
        createdAt: '2024-01-15T10:00:00Z',
        updatedAt: '2024-01-15T10:00:00Z',
      };

      mockCreateSession.mockResolvedValue(newSession);

      render(<SessionDashboard />);
      
      // Select a task
      const taskInput = screen.getByPlaceholderText('Select or enter task name...');
      fireEvent.change(taskInput, { target: { value: 'Development Work' } });
      
      // Click start button
      const startButton = screen.getByText('Start New Session');
      fireEvent.click(startButton);

      await waitFor(() => {
        expect(mockCreateSession).toHaveBeenCalledWith('task-1', 'Development Work');
      });
    });

    it('should show error when session creation fails', async () => {
      const errorMessage = 'Failed to create session';
      mockCreateSession.mockRejectedValue(new Error(errorMessage));

      render(<SessionDashboard />);
      
      const taskInput = screen.getByPlaceholderText('Select or enter task name...');
      fireEvent.change(taskInput, { target: { value: 'Development Work' } });
      
      const startButton = screen.getByText('Start New Session');
      fireEvent.click(startButton);

      await waitFor(() => {
        expect(mockShowError).toHaveBeenCalledWith(expect.stringContaining(errorMessage));
      });
    });

    it('should disable start button when no task is selected', () => {
      render(<SessionDashboard />);
      
      const startButton = screen.getByText('Start New Session');
      expect(startButton).toBeDisabled();
    });
  });

  describe('Session Display', () => {
    const mockSessions: TaskSession[] = [
      {
        id: 'session-1',
        taskId: 'task-1',
        taskName: 'Development Work',
        timerInstances: [
          {
            id: 'instance-1',
            sessionId: 'session-1',
            startTime: new Date('2024-01-15T10:00:00Z'),
            endTime: new Date('2024-01-15T11:00:00Z'),
            duration: 3600000, // 1 hour
            isRunning: false,
            isPaused: false,
            createdAt: '2024-01-15T10:00:00Z',
            updatedAt: '2024-01-15T11:00:00Z',
          },
        ],
        totalDuration: 3600000,
        isActive: false,
        date: '2024-01-15',
        createdAt: '2024-01-15T10:00:00Z',
        updatedAt: '2024-01-15T11:00:00Z',
      },
      {
        id: 'session-2',
        taskId: 'task-2',
        taskName: 'Code Review',
        timerInstances: [],
        totalDuration: 0,
        isActive: true,
        date: '2024-01-15',
        createdAt: '2024-01-15T11:00:00Z',
        updatedAt: '2024-01-15T11:00:00Z',
      },
    ];

    it('should display list of sessions', () => {
      vi.mocked(require('../../../hooks/useSessionManagement').useSessionManagement).mockReturnValue({
        sessions: mockSessions,
        activeSession: mockSessions[1],
        isLoading: false,
        error: null,
        createSession: mockCreateSession,
        updateSession: mockUpdateSession,
        deleteSession: mockDeleteSession,
        createTimerInstance: mockCreateTimerInstance,
        startTimer: mockStartTimer,
        stopTimer: mockStopTimer,
        pauseTimer: mockPauseTimer,
        resumeTimer: mockResumeTimer,
        refreshSessions: vi.fn(),
      });

      render(<SessionDashboard />);
      
      expect(screen.getByText('Development Work')).toBeInTheDocument();
      expect(screen.getByText('Code Review')).toBeInTheDocument();
      expect(screen.getByText('1:00:00')).toBeInTheDocument(); // 1 hour duration
    });

    it('should highlight active session', () => {
      vi.mocked(require('../../../hooks/useSessionManagement').useSessionManagement).mockReturnValue({
        sessions: mockSessions,
        activeSession: mockSessions[1],
        isLoading: false,
        error: null,
        createSession: mockCreateSession,
        updateSession: mockUpdateSession,
        deleteSession: mockDeleteSession,
        createTimerInstance: mockCreateTimerInstance,
        startTimer: mockStartTimer,
        stopTimer: mockStopTimer,
        pauseTimer: mockPauseTimer,
        resumeTimer: mockResumeTimer,
        refreshSessions: vi.fn(),
      });

      render(<SessionDashboard />);
      
      const activeSessionCard = screen.getByText('Code Review').closest('[data-testid="session-card"]');
      expect(activeSessionCard).toHaveClass('active');
    });
  });
});

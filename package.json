{"name": "taskmint", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "tauri": "tauri", "test": "vitest run", "test:watch": "vitest watch", "test:coverage": "vitest run --coverage", "test:ci": "vitest run --coverage --watchAll=false", "test:unit": "vitest run --exclude src/__tests__/integration/", "test:integration": "vitest run src/__tests__/integration/", "test:all": "vitest run --coverage --verbose", "test:sessions": "vitest run src/hooks/__tests__/useSessionManagement.test.ts src/services/__tests__/SessionService.test.ts", "test:inactivity": "vitest run src/hooks/__tests__/useInactivityDetection.test.ts", "test:migration": "vitest run src/services/__tests__/MigrationService.test.ts", "test:session-system": "vitest run src/__tests__/integration/SessionSystem.integration.test.tsx", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:headed": "playwright test --headed", "test:e2e:debug": "playwright test --debug", "test:e2e:sessions": "playwright test e2e/session-management.e2e.ts", "test:e2e:notes": "playwright test e2e/notes-functionality.e2e.ts", "test:e2e:inactivity": "playwright test e2e/inactivity-detection.e2e.ts", "test:e2e:workflows": "playwright test e2e/user-workflows.e2e.ts", "test:e2e:report": "playwright show-report"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^7.1.0", "@mui/material": "^7.1.0", "@mui/x-date-pickers": "^8.6.0", "@sentry/react": "^8.42.0", "@tauri-apps/api": "^2.5.0", "@tauri-apps/plugin-dialog": "^2.2.2", "@tauri-apps/plugin-notification": "^2", "@tauri-apps/plugin-opener": "^2", "@tauri-apps/plugin-store": "^2", "@types/react-window": "^1.8.8", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "moment": "^2.30.1", "react": "^18.3.1", "react-big-calendar": "^1.19.4", "react-dom": "^18.3.1", "react-window": "^1.8.11", "recharts": "^2.15.4", "zod": "^3.25.39"}, "devDependencies": {"@playwright/test": "^1.54.1", "@tauri-apps/cli": "^2.5.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/react": "^18.3.1", "@types/react-big-calendar": "^1.16.2", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.4", "@vitest/coverage-v8": "^3.2.4", "@vitest/ui": "^3.2.4", "jsdom": "^26.1.0", "rollup-plugin-visualizer": "^6.0.3", "typescript": "~5.6.2", "vite": "^6.0.3", "vitest": "^3.2.4"}}
<svg width="120" height="120" viewBox="0 0 120 120" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- TaskMint Logo - Stylized mint leaf with clock/productivity elements -->
  <defs>
    <linearGradient id="leafGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#65D6A1;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#4CAF50;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0D7377;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#0D7377;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#004D40;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Main mint leaf shape -->
  <path d="M35 25 C65 25, 85 45, 85 75 C85 95, 75 105, 60 105 C45 105, 35 95, 35 75 C35 55, 36 35, 35 25 Z" 
        fill="url(#leafGradient)" opacity="0.95"/>
  
  <!-- Secondary leaf for depth -->
  <path d="M45 30 C70 30, 80 50, 80 70 C80 85, 72 95, 60 95 C48 95, 40 85, 40 70 C40 55, 42 40, 45 30 Z" 
        fill="url(#accentGradient)" opacity="0.3"/>
  
  <!-- Central stem/vein -->
  <path d="M60 25 L60 105" stroke="#0D7377" stroke-width="3" opacity="0.8"/>
  
  <!-- Side veins -->
  <path d="M60 40 Q50 45, 45 55" stroke="#0D7377" stroke-width="1.5" opacity="0.6"/>
  <path d="M60 50 Q70 55, 75 65" stroke="#0D7377" stroke-width="1.5" opacity="0.6"/>
  <path d="M60 60 Q50 65, 45 75" stroke="#0D7377" stroke-width="1.5" opacity="0.6"/>
  <path d="M60 70 Q70 75, 75 85" stroke="#0D7377" stroke-width="1.5" opacity="0.6"/>
  
  <!-- Productivity checkmark integrated into leaf -->
  <path d="M50 65 L57 72 L70 55" stroke="#FAFAFA" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" opacity="0.9"/>
  
  <!-- Subtle clock dots around the leaf -->
  <circle cx="60" cy="35" r="2" fill="#0D7377" opacity="0.7"/>
  <circle cx="75" cy="60" r="2" fill="#0D7377" opacity="0.7"/>
  <circle cx="60" cy="85" r="2" fill="#0D7377" opacity="0.7"/>
  <circle cx="45" cy="60" r="2" fill="#0D7377" opacity="0.7"/>
</svg>

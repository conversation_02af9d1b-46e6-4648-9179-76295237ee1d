# Google Drive API Configuration
# Copy this file to .env and replace with your actual Google OAuth credentials
# To get these credentials:
# 1. Go to https://console.developers.google.com/
# 2. Create a new project or select an existing one
# 3. Enable the Google Drive API
# 4. Create OAuth 2.0 credentials
# 5. Add http://localhost:3000/auth/callback to authorized redirect URIs

VITE_GOOGLE_CLIENT_ID=your_google_client_id_here
VITE_GOOGLE_CLIENT_SECRET=your_google_client_secret_here

# Subscription and Marketing
VITE_WEBSITE_URL=https://taskmint.app

# Sentry Configuration
VITE_SENTRY_DSN=your_sentry_dsn_here

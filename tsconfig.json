{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,

    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",
    "types": ["vitest/globals"],

    /* Linting */
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,
    "allowSyntheticDefaultImports": true
  },
  "include": ["src"],
  "exclude": [
    "node_modules",
    "dist",
    "src/__mocks__",
    "src/**/__tests__",
    "src/setupTests.ts",
    "src/__tests__/setup/import-meta-mock.ts",
    "src/__tests__/integration/test-setup.ts",
    "src-tauri"
  ],
  "references": [{ "path": "./tsconfig.node.json" }]
}
